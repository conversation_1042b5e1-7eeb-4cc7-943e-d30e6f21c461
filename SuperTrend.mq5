//+------------------------------------------------------------------+
//|                                                SuperTrend_EA.mq5 |
//|                                  Copyright 2024, Augment Agent   |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Augment Agent"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "SuperTrend Expert Advisor with trend reversal strategy"

//--- Include necessary libraries
#include <Trade\Trade.mqh>

//--- Trading direction enumeration
enum ENUM_TRADE_DIRECTION
{
    TRADE_BOTH,     // Both Buy and Sell
    TRADE_BUY_ONLY, // Buy Only
    TRADE_SELL_ONLY // Sell Only
};

//--- Input parameters
input group "=== SuperTrend Settings ==="
input int      ATR_Period = 10;           // ATR Period
input double   Multiplier = 3.0;          // SuperTrend Multiplier

input group "=== Trading Settings ==="
input double   LotSize = 0.1;             // Lot Size
input ENUM_TIMEFRAMES Timeframe = PERIOD_M15; // Trading Timeframe
input int      StopLoss = 2000;           // Stop Loss in points
input int      TakeProfit = 2000;         // Take Profit in points

input group "=== Optional Settings ==="
input bool     UseTrailingStop = true;    // Use Trailing Stop
input int      TrailingStop = 250;        // Trailing Stop in points
input bool     UseTimeFilter = true;      // Use Time Filter
input int      StartHour = 8;             // Trading Start Hour
input int      EndHour = 17;              // Trading End Hour

input group "=== Risk Management ==="
input int      MaxSpread = 50;            // Maximum Spread in points
input int      Slippage = 10;             // Maximum Slippage in points
input bool     OneTradePerSignal = true;  // Allow only one trade per signal

input group "=== Recovery System ==="
input bool     UseRecovery = true;        // Enable Recovery System
input int      RecoveryTrigger = 15000;   // Recovery trigger in points (loss before adding position)
input int      MaxRecoveryTrades = 5;     // Maximum number of recovery trades
input double   RecoveryLotSize = 0.02;    // Lot size for each recovery trade

input group "=== Trading Direction ==="
input ENUM_TRADE_DIRECTION TradeDirection = TRADE_BOTH; // Trading Direction

input group "=== RSI Divergence Filter ==="
input bool     UseRSIDivergence = true;       // Enable RSI Divergence Filter
input int      RSI_Period = 14;               // RSI Period
input int      DivergenceLookback = 5;        // Bars to look back for divergence
input double   MinDivergenceStrength = 2.0;   // Minimum divergence strength
input bool     RequireDivergence = false;     // Require divergence for all trades

//--- Global variables
CTrade trade;
int atr_handle;
int rsi_handle;
double atr_buffer[];
double rsi_buffer[];
double high_buffer[], low_buffer[], close_buffer[];
double supertrend_upper[], supertrend_lower[];
bool supertrend_trend[];
int bars_total;
datetime last_bar_time;
datetime last_trade_time;        // Time of last trade to prevent multiple trades per bar
bool last_signal_processed;      // Flag to track if current signal was already processed
int last_trade_type;             // Track last trade type: 0=none, 1=buy, 2=sell
bool waiting_for_opposite_signal; // Flag to wait for opposite signal after trade

//--- Recovery system variables
bool recovery_active;            // Flag indicating if recovery is active
double original_sl_buy;          // Original stop loss for buy positions
double original_tp_buy;          // Original take profit for buy positions
double original_sl_sell;         // Original stop loss for sell positions
double original_tp_sell;         // Original take profit for sell positions
int recovery_count;              // Number of recovery trades opened
double last_checked_loss;        // Last checked loss amount to prevent multiple triggers

//--- Magic number for orders
#define MAGIC_NUMBER 123456

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Set trade parameters
    trade.SetExpertMagicNumber(MAGIC_NUMBER);
    trade.SetDeviationInPoints(Slippage);
    
    //--- Create ATR indicator handle
    atr_handle = iATR(_Symbol, Timeframe, ATR_Period);
    if(atr_handle == INVALID_HANDLE)
    {
        Print("Failed to create ATR indicator handle");
        return INIT_FAILED;
    }

    //--- Create RSI indicator handle
    rsi_handle = iRSI(_Symbol, Timeframe, RSI_Period, PRICE_CLOSE);
    if(rsi_handle == INVALID_HANDLE)
    {
        Print("Failed to create RSI indicator handle");
        return INIT_FAILED;
    }
    
    //--- Initialize arrays
    ArraySetAsSeries(atr_buffer, true);
    ArraySetAsSeries(rsi_buffer, true);
    ArraySetAsSeries(high_buffer, true);
    ArraySetAsSeries(low_buffer, true);
    ArraySetAsSeries(close_buffer, true);
    ArraySetAsSeries(supertrend_upper, true);
    ArraySetAsSeries(supertrend_lower, true);
    ArraySetAsSeries(supertrend_trend, true);
    
    //--- Get initial bar count
    bars_total = iBars(_Symbol, Timeframe);
    last_bar_time = iTime(_Symbol, Timeframe, 0);

    //--- Initialize trade control variables
    last_trade_time = 0;
    last_signal_processed = false;
    last_trade_type = 0;              // 0=none, 1=buy, 2=sell
    waiting_for_opposite_signal = false;

    //--- Initialize recovery variables
    recovery_active = false;
    original_sl_buy = 0;
    original_tp_buy = 0;
    original_sl_sell = 0;
    original_tp_sell = 0;
    recovery_count = 0;
    last_checked_loss = 0;

    Print("SuperTrend EA initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release indicator handles
    if(atr_handle != INVALID_HANDLE)
        IndicatorRelease(atr_handle);
    if(rsi_handle != INVALID_HANDLE)
        IndicatorRelease(rsi_handle);

    Print("SuperTrend EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check for new bar
    if(!IsNewBar())
        return;
    
    //--- Check time filter
    if(UseTimeFilter && !IsTimeToTrade())
        return;
    
    //--- Check spread
    if(GetSpread() > MaxSpread)
        return;
    
    //--- Calculate SuperTrend
    if(!CalculateSuperTrend())
        return;
    
    //--- Handle trailing stop
    if(UseTrailingStop)
        HandleTrailingStop();

    //--- Check recovery system
    if(UseRecovery)
        CheckRecoverySystem();

    //--- Check for entry signals
    CheckEntrySignals();

    //--- Check for exit signals
    CheckExitSignals();
}

//+------------------------------------------------------------------+
//| Check if new bar formed                                          |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime current_time = iTime(_Symbol, Timeframe, 0);
    if(current_time != last_bar_time)
    {
        last_bar_time = current_time;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if it's time to trade                                      |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    
    if(StartHour <= EndHour)
        return (dt.hour >= StartHour && dt.hour < EndHour);
    else
        return (dt.hour >= StartHour || dt.hour < EndHour);
}

//+------------------------------------------------------------------+
//| Get current spread in points                                     |
//+------------------------------------------------------------------+
int GetSpread()
{
    return (int)((Ask() - Bid()) / _Point);
}

//+------------------------------------------------------------------+
//| Calculate SuperTrend indicator                                   |
//+------------------------------------------------------------------+
bool CalculateSuperTrend()
{
    //--- Get required data
    if(CopyBuffer(atr_handle, 0, 0, 3, atr_buffer) < 3)
        return false;
    
    if(CopyHigh(_Symbol, Timeframe, 0, 3, high_buffer) < 3)
        return false;
    
    if(CopyLow(_Symbol, Timeframe, 0, 3, low_buffer) < 3)
        return false;
    
    if(CopyClose(_Symbol, Timeframe, 0, 3, close_buffer) < 3)
        return false;
    
    //--- Resize SuperTrend arrays
    ArrayResize(supertrend_upper, 3);
    ArrayResize(supertrend_lower, 3);
    ArrayResize(supertrend_trend, 3);
    
    //--- Calculate SuperTrend for each bar
    for(int i = 2; i >= 0; i--)
    {
        double hl2 = (high_buffer[i] + low_buffer[i]) / 2.0;
        double atr_value = atr_buffer[i];
        
        //--- Calculate basic upper and lower bands
        double basic_upper = hl2 + (Multiplier * atr_value);
        double basic_lower = hl2 - (Multiplier * atr_value);
        
        //--- Calculate final upper and lower bands
        if(i == 2) // First calculation
        {
            supertrend_upper[i] = basic_upper;
            supertrend_lower[i] = basic_lower;
        }
        else
        {
            supertrend_upper[i] = (basic_upper < supertrend_upper[i+1] || close_buffer[i+1] > supertrend_upper[i+1]) ? 
                                  basic_upper : supertrend_upper[i+1];
            
            supertrend_lower[i] = (basic_lower > supertrend_lower[i+1] || close_buffer[i+1] < supertrend_lower[i+1]) ? 
                                  basic_lower : supertrend_lower[i+1];
        }
        
        //--- Determine trend direction
        if(i == 2) // First calculation
        {
            supertrend_trend[i] = close_buffer[i] <= supertrend_lower[i];
        }
        else
        {
            if(supertrend_trend[i+1] && close_buffer[i] > supertrend_lower[i])
                supertrend_trend[i] = false; // Bullish
            else if(!supertrend_trend[i+1] && close_buffer[i] < supertrend_upper[i])
                supertrend_trend[i] = true;  // Bearish
            else
                supertrend_trend[i] = supertrend_trend[i+1]; // No change
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check for entry signals                                          |
//+------------------------------------------------------------------+
void CheckEntrySignals()
{
    //--- Check if we already have positions (skip if recovery is active and we have positions)
    bool has_positions = false;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MAGIC_NUMBER)
        {
            has_positions = true;
            break;
        }
    }

    if(has_positions && !recovery_active)
        return;

    //--- Check for pending orders
    if(HasPendingOrders())
        return;

    //--- Check for trend change signals
    if(ArraySize(supertrend_trend) < 2)
        return;

    //--- Detect signals
    bool current_signal_buy = (supertrend_trend[1] == true && supertrend_trend[0] == false);
    bool current_signal_sell = (supertrend_trend[1] == false && supertrend_trend[0] == true);

    //--- Prevent multiple trades on the same bar
    datetime current_bar_time = iTime(_Symbol, Timeframe, 0);
    if(OneTradePerSignal && last_trade_time == current_bar_time)
        return;

    //--- OneTradePerSignal Logic: Modified for direction-only trading
    if(OneTradePerSignal && waiting_for_opposite_signal)
    {
        if(TradeDirection == TRADE_BUY_ONLY)
        {
            // In Buy Only mode, reset waiting flag when we get a bearish signal (even though we won't trade it)
            // This allows the next bullish signal to open a new buy trade
            if(current_signal_sell)
            {
                waiting_for_opposite_signal = false;
                last_trade_type = 0;
                Print("Bearish signal detected in Buy Only mode - ready for next buy signal");
            }
            // Don't allow new buy until we've seen the opposite signal
            if(last_trade_type == 1 && !current_signal_sell)
                return;
        }
        else if(TradeDirection == TRADE_SELL_ONLY)
        {
            // In Sell Only mode, reset waiting flag when we get a bullish signal (even though we won't trade it)
            // This allows the next bearish signal to open a new sell trade
            if(current_signal_buy)
            {
                waiting_for_opposite_signal = false;
                last_trade_type = 0;
                Print("Bullish signal detected in Sell Only mode - ready for next sell signal");
            }
            // Don't allow new sell until we've seen the opposite signal
            if(last_trade_type == 2 && !current_signal_buy)
                return;
        }
        else // TRADE_BOTH mode
        {
            // If last trade was BUY (1), only allow SELL signals
            if(last_trade_type == 1 && !current_signal_sell)
                return;

            // If last trade was SELL (2), only allow BUY signals
            if(last_trade_type == 2 && !current_signal_buy)
                return;
        }
    }

    //--- Buy signal: SuperTrend changes from Red to Green (bearish to bullish)
    if(current_signal_buy && IsTradeDirectionAllowed(POSITION_TYPE_BUY))
    {
        bool rsi_signal_valid = true;

        //--- Check RSI divergence if enabled
        if(UseRSIDivergence)
        {
            rsi_signal_valid = CheckRSIDivergence(true); // Check for bullish divergence
            if(RequireDivergence && !rsi_signal_valid)
            {
                Print("Buy signal ignored - no bullish RSI divergence found");
                return;
            }
        }

        if(OpenBuyPosition())
        {
            if(OneTradePerSignal)
            {
                last_trade_time = current_bar_time;
                // Reset flags from previous trade and set new state
                waiting_for_opposite_signal = true;
                last_trade_type = 1;  // Remember this was a BUY trade - now wait for SELL
            }
            string divergence_info = rsi_signal_valid ? " with RSI divergence" : "";
            Print("Buy signal processed at ", TimeToString(current_bar_time), divergence_info);
        }
    }
    //--- Sell signal: SuperTrend changes from Green to Red (bullish to bearish)
    else if(current_signal_sell && IsTradeDirectionAllowed(POSITION_TYPE_SELL))
    {
        bool rsi_signal_valid = true;

        //--- Check RSI divergence if enabled
        if(UseRSIDivergence)
        {
            rsi_signal_valid = CheckRSIDivergence(false); // Check for bearish divergence
            if(RequireDivergence && !rsi_signal_valid)
            {
                Print("Sell signal ignored - no bearish RSI divergence found");
                return;
            }
        }

        if(OpenSellPosition())
        {
            if(OneTradePerSignal)
            {
                last_trade_time = current_bar_time;
                // Reset flags from previous trade and set new state
                waiting_for_opposite_signal = true;
                last_trade_type = 2;  // Remember this was a SELL trade - now wait for BUY
            }
            string divergence_info = rsi_signal_valid ? " with RSI divergence" : "";
            Print("Sell signal processed at ", TimeToString(current_bar_time), divergence_info);
        }
    }
}

//+------------------------------------------------------------------+
//| Check for exit signals                                           |
//+------------------------------------------------------------------+
void CheckExitSignals()
{
    //--- Check if we have positions
    if(PositionsTotal() == 0)
        return;

    //--- Check all positions for exit signals
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MAGIC_NUMBER)
        {
            long position_type = PositionGetInteger(POSITION_TYPE);

            //--- Close Buy positions when SuperTrend turns Red (bearish)
            if(position_type == POSITION_TYPE_BUY && supertrend_trend[0] == true)
            {
                CloseBuyPosition();
                // DON'T reset flags - keep waiting for opposite signal
                // This ensures we wait for SELL signal after BUY position closes
                break; // Exit loop after closing all buy positions
            }
            //--- Close Sell positions when SuperTrend turns Green (bullish)
            else if(position_type == POSITION_TYPE_SELL && supertrend_trend[0] == false)
            {
                CloseSellPosition();
                // DON'T reset flags - keep waiting for opposite signal
                // This ensures we wait for BUY signal after SELL position closes
                break; // Exit loop after closing all sell positions
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Open Buy Position                                                |
//+------------------------------------------------------------------+
bool OpenBuyPosition()
{
    double ask = Ask();
    double sl = (StopLoss > 0) ? ask - (StopLoss * _Point) : 0;
    double tp = (TakeProfit > 0) ? ask + (TakeProfit * _Point) : 0;

    if(trade.Buy(LotSize, _Symbol, ask, sl, tp, "SuperTrend Buy"))
    {
        //--- Store original SL/TP for recovery system
        if(UseRecovery && !recovery_active)
        {
            original_sl_buy = sl;
            original_tp_buy = tp;
            recovery_active = true;
            recovery_count = 0;
            last_checked_loss = 0;
            Print("Recovery system activated for BUY. Original SL: ", original_sl_buy, " TP: ", original_tp_buy);
        }

        Print("Buy order opened successfully at ", ask);
        return true;
    }
    else
    {
        Print("Failed to open buy order. Error: ", trade.ResultRetcode());
        return false;
    }
}

//+------------------------------------------------------------------+
//| Open Sell Position                                               |
//+------------------------------------------------------------------+
bool OpenSellPosition()
{
    double bid = Bid();
    double sl = (StopLoss > 0) ? bid + (StopLoss * _Point) : 0;
    double tp = (TakeProfit > 0) ? bid - (TakeProfit * _Point) : 0;

    if(trade.Sell(LotSize, _Symbol, bid, sl, tp, "SuperTrend Sell"))
    {
        //--- Store original SL/TP for recovery system
        if(UseRecovery && !recovery_active)
        {
            original_sl_sell = sl;
            original_tp_sell = tp;
            recovery_active = true;
            recovery_count = 0;
            last_checked_loss = 0;
            Print("Recovery system activated for SELL. Original SL: ", original_sl_sell, " TP: ", original_tp_sell);
        }

        Print("Sell order opened successfully at ", bid);
        return true;
    }
    else
    {
        Print("Failed to open sell order. Error: ", trade.ResultRetcode());
        return false;
    }
}

//+------------------------------------------------------------------+
//| Close Buy Position                                               |
//+------------------------------------------------------------------+
void CloseBuyPosition()
{
    //--- Close all BUY positions (including recovery positions)
    bool all_closed = true;
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetSymbol(i) == _Symbol)
        {
            if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY &&
               PositionGetInteger(POSITION_MAGIC) == MAGIC_NUMBER)
            {
                ulong ticket = PositionGetInteger(POSITION_TICKET);
                if(!trade.PositionClose(ticket))
                {
                    all_closed = false;
                    Print("Failed to close buy position #", ticket, ". Error: ", trade.ResultRetcode());
                }
                else
                {
                    Print("Buy position #", ticket, " closed successfully");
                }
            }
        }
    }

    if(all_closed)
    {
        //--- Reset recovery system when all positions are closed
        ResetRecoverySystem();
        // DON'T reset flags here - keep waiting for opposite signal
        // Flags will only reset when opposite signal actually executes
    }
}

//+------------------------------------------------------------------+
//| Close Sell Position                                              |
//+------------------------------------------------------------------+
void CloseSellPosition()
{
    //--- Close all SELL positions (including recovery positions)
    bool all_closed = true;
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetSymbol(i) == _Symbol)
        {
            if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL &&
               PositionGetInteger(POSITION_MAGIC) == MAGIC_NUMBER)
            {
                ulong ticket = PositionGetInteger(POSITION_TICKET);
                if(!trade.PositionClose(ticket))
                {
                    all_closed = false;
                    Print("Failed to close sell position #", ticket, ". Error: ", trade.ResultRetcode());
                }
                else
                {
                    Print("Sell position #", ticket, " closed successfully");
                }
            }
        }
    }

    if(all_closed)
    {
        //--- Reset recovery system when all positions are closed
        ResetRecoverySystem();
        // DON'T reset flags here - keep waiting for opposite signal
        // Flags will only reset when opposite signal actually executes
    }
}

//+------------------------------------------------------------------+
//| Handle Trailing Stop                                             |
//+------------------------------------------------------------------+
void HandleTrailingStop()
{
    if(!PositionSelect(_Symbol))
        return;

    long position_type = PositionGetInteger(POSITION_TYPE);
    double position_open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    double position_sl = PositionGetDouble(POSITION_SL);
    double current_price = (position_type == POSITION_TYPE_BUY) ? Bid() : Ask();

    double trailing_distance = TrailingStop * _Point;
    double new_sl = 0;
    bool modify_needed = false;

    if(position_type == POSITION_TYPE_BUY)
    {
        //--- Calculate new stop loss for buy position
        new_sl = current_price - trailing_distance;

        //--- Check if we need to modify
        if(new_sl > position_sl + _Point || position_sl == 0)
        {
            modify_needed = true;
        }
    }
    else if(position_type == POSITION_TYPE_SELL)
    {
        //--- Calculate new stop loss for sell position
        new_sl = current_price + trailing_distance;

        //--- Check if we need to modify
        if(new_sl < position_sl - _Point || position_sl == 0)
        {
            modify_needed = true;
        }
    }

    //--- Modify position if needed
    if(modify_needed)
    {
        double tp = PositionGetDouble(POSITION_TP);
        if(trade.PositionModify(_Symbol, new_sl, tp))
        {
            Print("Trailing stop updated to ", new_sl);
        }
    }
}

//+------------------------------------------------------------------+
//| Check if there are pending orders                               |
//+------------------------------------------------------------------+
bool HasPendingOrders()
{
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(OrderGetTicket(i)))
        {
            if(OrderGetString(ORDER_SYMBOL) == _Symbol &&
               OrderGetInteger(ORDER_MAGIC) == MAGIC_NUMBER)
            {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Get Ask price                                                    |
//+------------------------------------------------------------------+
double Ask()
{
    MqlTick tick;
    if(SymbolInfoTick(_Symbol, tick))
        return tick.ask;
    return 0;
}

//+------------------------------------------------------------------+
//| Get Bid price                                                    |
//+------------------------------------------------------------------+
double Bid()
{
    MqlTick tick;
    if(SymbolInfoTick(_Symbol, tick))
        return tick.bid;
    return 0;
}

//+------------------------------------------------------------------+
//| Check Recovery System                                            |
//+------------------------------------------------------------------+
void CheckRecoverySystem()
{
    if(!recovery_active)
        return;

    //--- Check if we have positions
    if(!PositionSelect(_Symbol))
    {
        ResetRecoverySystem();
        return;
    }

    //--- Get current position info
    long position_type = PositionGetInteger(POSITION_TYPE);
    double current_profit = PositionGetDouble(POSITION_PROFIT);

    //--- Check if loss reaches recovery trigger
    if(current_profit < 0)
    {
        // Calculate loss in account currency (already in correct format)
        double current_loss_amount = MathAbs(current_profit);

        // Convert to points for comparison with RecoveryTrigger
        double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
        double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
        double point_value = tick_value * (_Point / tick_size);
        double current_loss_points = current_loss_amount / point_value;

        //--- Check if we need to add recovery position
        if(current_loss_points >= RecoveryTrigger &&
           current_loss_points > last_checked_loss + (RecoveryTrigger * 0.1) && // Prevent multiple triggers
           recovery_count < MaxRecoveryTrades)
        {
            if(position_type == POSITION_TYPE_BUY && IsTradeDirectionAllowed(POSITION_TYPE_BUY))
            {
                OpenRecoveryBuy();
            }
            else if(position_type == POSITION_TYPE_SELL && IsTradeDirectionAllowed(POSITION_TYPE_SELL))
            {
                OpenRecoverySell();
            }

            last_checked_loss = current_loss_points;
        }
    }
}

//+------------------------------------------------------------------+
//| Open Recovery Buy Position                                       |
//+------------------------------------------------------------------+
void OpenRecoveryBuy()
{
    double ask = Ask();

    if(trade.Buy(RecoveryLotSize, _Symbol, ask, original_sl_buy, original_tp_buy, "Recovery Buy #" + IntegerToString(recovery_count + 1)))
    {
        recovery_count++;
        Print("Recovery BUY #", recovery_count, " opened at ", ask,
              " | Lot: ", RecoveryLotSize,
              " | Using original SL: ", original_sl_buy,
              " | TP: ", original_tp_buy);
    }
    else
    {
        Print("Failed to open recovery buy. Error: ", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| Open Recovery Sell Position                                      |
//+------------------------------------------------------------------+
void OpenRecoverySell()
{
    double bid = Bid();

    if(trade.Sell(RecoveryLotSize, _Symbol, bid, original_sl_sell, original_tp_sell, "Recovery Sell #" + IntegerToString(recovery_count + 1)))
    {
        recovery_count++;
        Print("Recovery SELL #", recovery_count, " opened at ", bid,
              " | Lot: ", RecoveryLotSize,
              " | Using original SL: ", original_sl_sell,
              " | TP: ", original_tp_sell);
    }
    else
    {
        Print("Failed to open recovery sell. Error: ", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| Reset Recovery System                                            |
//+------------------------------------------------------------------+
void ResetRecoverySystem()
{
    recovery_active = false;
    original_sl_buy = 0;
    original_tp_buy = 0;
    original_sl_sell = 0;
    original_tp_sell = 0;
    recovery_count = 0;
    last_checked_loss = 0;

    Print("Recovery system reset");
}

//+------------------------------------------------------------------+
//| Check if trade direction is allowed                             |
//+------------------------------------------------------------------+
bool IsTradeDirectionAllowed(ENUM_POSITION_TYPE position_type)
{
    switch(TradeDirection)
    {
        case TRADE_BOTH:
            return true;

        case TRADE_BUY_ONLY:
            return (position_type == POSITION_TYPE_BUY);

        case TRADE_SELL_ONLY:
            return (position_type == POSITION_TYPE_SELL);

        default:
            return true;
    }
}

//+------------------------------------------------------------------+
//| Check RSI Divergence                                             |
//+------------------------------------------------------------------+
bool CheckRSIDivergence(bool bullish_divergence)
{
    //--- Get RSI data
    if(CopyBuffer(rsi_handle, 0, 0, DivergenceLookback + 2, rsi_buffer) < DivergenceLookback + 2)
        return false;

    //--- Get price data for divergence comparison
    if(CopyHigh(_Symbol, Timeframe, 0, DivergenceLookback + 2, high_buffer) < DivergenceLookback + 2)
        return false;

    if(CopyLow(_Symbol, Timeframe, 0, DivergenceLookback + 2, low_buffer) < DivergenceLookback + 2)
        return false;

    if(bullish_divergence)
    {
        return CheckBullishDivergence();
    }
    else
    {
        return CheckBearishDivergence();
    }
}

//+------------------------------------------------------------------+
//| Check Bullish Divergence (Price Lower Lows, RSI Higher Lows)    |
//+------------------------------------------------------------------+
bool CheckBullishDivergence()
{
    //--- Find recent low points in price and RSI
    double price_low1 = low_buffer[0];
    double rsi_low1 = rsi_buffer[0];
    int price_low1_index = 0;
    int rsi_low1_index = 0;

    double price_low2 = 999999;
    double rsi_low2 = 100;
    int price_low2_index = -1;
    int rsi_low2_index = -1;

    //--- Look for previous low points
    for(int i = 2; i <= DivergenceLookback; i++)
    {
        //--- Check for price low
        if(low_buffer[i] < price_low2 && low_buffer[i] < low_buffer[i-1] && low_buffer[i] < low_buffer[i+1])
        {
            price_low2 = low_buffer[i];
            price_low2_index = i;
        }

        //--- Check for RSI low
        if(rsi_buffer[i] < rsi_low2 && rsi_buffer[i] < rsi_buffer[i-1] && rsi_buffer[i] < rsi_buffer[i+1])
        {
            rsi_low2 = rsi_buffer[i];
            rsi_low2_index = i;
        }
    }

    //--- Check if we found valid low points
    if(price_low2_index == -1 || rsi_low2_index == -1)
        return false;

    //--- Check for bullish divergence: price makes lower low, RSI makes higher low
    bool price_lower_low = price_low1 < price_low2;
    bool rsi_higher_low = rsi_low1 > rsi_low2;

    //--- Calculate divergence strength
    double price_diff = MathAbs(price_low1 - price_low2) / _Point;
    double rsi_diff = MathAbs(rsi_low1 - rsi_low2);

    bool strong_divergence = (price_diff >= MinDivergenceStrength) && (rsi_diff >= 1.0);

    if(price_lower_low && rsi_higher_low && strong_divergence)
    {
        Print("Bullish RSI divergence detected - Price: ", price_low1, " vs ", price_low2,
              " | RSI: ", rsi_low1, " vs ", rsi_low2);
        return true;
    }

    return false;
}

//+------------------------------------------------------------------+
//| Check Bearish Divergence (Price Higher Highs, RSI Lower Highs)  |
//+------------------------------------------------------------------+
bool CheckBearishDivergence()
{
    //--- Find recent high points in price and RSI
    double price_high1 = high_buffer[0];
    double rsi_high1 = rsi_buffer[0];
    int price_high1_index = 0;
    int rsi_high1_index = 0;

    double price_high2 = 0;
    double rsi_high2 = 0;
    int price_high2_index = -1;
    int rsi_high2_index = -1;

    //--- Look for previous high points
    for(int i = 2; i <= DivergenceLookback; i++)
    {
        //--- Check for price high
        if(high_buffer[i] > price_high2 && high_buffer[i] > high_buffer[i-1] && high_buffer[i] > high_buffer[i+1])
        {
            price_high2 = high_buffer[i];
            price_high2_index = i;
        }

        //--- Check for RSI high
        if(rsi_buffer[i] > rsi_high2 && rsi_buffer[i] > rsi_buffer[i-1] && rsi_buffer[i] > rsi_buffer[i+1])
        {
            rsi_high2 = rsi_buffer[i];
            rsi_high2_index = i;
        }
    }

    //--- Check if we found valid high points
    if(price_high2_index == -1 || rsi_high2_index == -1)
        return false;

    //--- Check for bearish divergence: price makes higher high, RSI makes lower high
    bool price_higher_high = price_high1 > price_high2;
    bool rsi_lower_high = rsi_high1 < rsi_high2;

    //--- Calculate divergence strength
    double price_diff = MathAbs(price_high1 - price_high2) / _Point;
    double rsi_diff = MathAbs(rsi_high1 - rsi_high2);

    bool strong_divergence = (price_diff >= MinDivergenceStrength) && (rsi_diff >= 1.0);

    if(price_higher_high && rsi_lower_high && strong_divergence)
    {
        Print("Bearish RSI divergence detected - Price: ", price_high1, " vs ", price_high2,
              " | RSI: ", rsi_high1, " vs ", rsi_high2);
        return true;
    }

    return false;
}
